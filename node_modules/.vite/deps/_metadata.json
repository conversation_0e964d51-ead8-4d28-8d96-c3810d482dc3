{"hash": "9b2677fa", "browserHash": "b5e34ff0", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "dfb6efc3", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d8634b73", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b9590be7", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7e64a522", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ffc5ef11", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "0484599c", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "b85475fe", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "d8a6a6df", "needsInterop": false}}, "chunks": {"browser-ZONL5W77": {"file": "browser-ZONL5W77.js"}, "chunk-G52XTN3B": {"file": "chunk-G52XTN3B.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "chunk-QH3POG6S": {"file": "chunk-QH3POG6S.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}