import React from 'react';

const StoreLocatorTest: React.FC = () => {
  console.log('🚨 STORE LOCATOR TEST: Component is rendering!');
  
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-6 py-12">
        <h1 className="text-4xl font-bold text-wellness mb-4">Store Locator Test</h1>
        <p className="text-lg text-wellness">
          This is a minimal test version to check if the Store Locator route works.
        </p>
        <div className="mt-8 p-4 border border-wellness rounded-lg">
          <p className="text-wellness">If you can see this, the route and basic component rendering works.</p>
          <p className="text-wellness mt-2">The issue is likely in the complex StoreLocator component logic.</p>
        </div>
      </div>
    </div>
  );
};

export default StoreLocatorTest;
