import React from 'react';

interface StoreLocatorProps {
  productId?: string;
  category?: string;
}

const StoreLocatorMinimal: React.FC<StoreLocatorProps> = ({ productId = 'all', category = 'all' }) => {
  console.log('🚨 STORE LOCATOR MINIMAL: Component rendering with productId:', productId, 'category:', category);

  return (
    <div className="min-h-screen bg-white">
      <div className="bg-white border-b border-wellness">
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-wellness mb-4">Find NRT Stores Near You</h1>
            <p className="text-lg text-wellness max-w-2xl mx-auto">
              Locate pharmacies, supermarkets, and stores that sell NRT products in your area.
              Get real-time inventory and pricing information.
            </p>
            <p className="text-sm text-wellness mt-2 font-medium">
              When cravings hit, find help nearby.
            </p>
          </div>
        </div>
      </div>

      <main className="py-8">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="bg-white rounded-xl shadow-sm border border-wellness p-6 mb-6">
            <div className="text-center">
              <p className="text-wellness text-lg">
                Store Locator is loading... If you see this message, the component structure works!
              </p>
              <p className="text-wellness mt-4">
                The issue is likely in the complex hooks and data fetching logic.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default StoreLocatorMinimal;
