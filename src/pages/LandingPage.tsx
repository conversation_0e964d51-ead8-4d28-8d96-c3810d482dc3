import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Search, Menu, X, DollarSign, Users, Star, ArrowRight, Package, Target, Store, BookOpen, Zap, Award, Shield, CheckCircle } from 'lucide-react';
import { getProducts, getTestimonials, getVendors } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from '../components/AuthModal';
import { ProductCardSkeleton, TestimonialSkeleton, VendorSkeleton } from '../components/SkeletonLoader';


const LandingPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');
  const [products, setProducts] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [newsletterStatus, setNewsletterStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [newsletterMessage, setNewsletterMessage] = useState('');

  // Clean search handler for production
  const handleSearch = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Clean search input handler
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Newsletter signup handler with validation
  const handleNewsletterSignup = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const email = formData.get('email') as string;

    if (!email || !email.includes('@') || !email.includes('.')) {
      setNewsletterStatus('error');
      setNewsletterMessage('Please enter a valid email address');
      return;
    }

    setNewsletterStatus('loading');
    setNewsletterMessage('');

    try {
      // Simulate API call - in production, integrate with email service
      await new Promise(resolve => setTimeout(resolve, 1000));

      setNewsletterStatus('success');
      setNewsletterMessage('Thank you! You\'ve been subscribed to our newsletter.');
      setNewsletterEmail('');
      e.currentTarget.reset();

      // Reset success message after 5 seconds
      setTimeout(() => {
        setNewsletterStatus('idle');
        setNewsletterMessage('');
      }, 5000);
    } catch (error) {
      setNewsletterStatus('error');
      setNewsletterMessage('Something went wrong. Please try again.');
    }
  };



  // Load data from database
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [productData, testimonialsData, vendorsData] = await Promise.all([
          getProducts(),
          getTestimonials(),
          getVendors()
        ]);

        setProducts(productData);
        setTestimonials(testimonialsData);
        setVendors(vendorsData);
        setError(null);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      {/* Apple-style Navigation Header with macOS Standards */}
      <nav className="sticky top-0 z-50 bg-white backdrop-blur-xl border-b border-wellness" role="navigation" aria-label="Main navigation">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
          <div className="flex items-center justify-between h-20">
            {/* Logo - Apple-style Professional Branding */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity duration-200" aria-label="NRTList homepage">
                <div className="w-10 h-10 bg-wellness rounded-xl flex items-center justify-center shadow-sm" role="img" aria-label="NRTList logo">
                  <span className="text-white font-bold text-lg">N</span>
                </div>
                <span className="text-2xl font-bold text-wellness tracking-tight">NRTList</span>
              </Link>
            </div>

            {/* Apple-style Navigation with Perfect Visual Balance */}
            <div className="hidden lg:flex items-center space-x-1">
              <Link to="/nrt-directory" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                NRT Directory
              </Link>
              <Link to="/smokeless-alternatives" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Smokeless Alternatives
              </Link>
              <Link to="/stores" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Store Locator
              </Link>
              <Link to="/vendors" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Online Vendors
              </Link>
              <Link to="/price-compare" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Price Compare
              </Link>
              <Link to="/reviews" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Reviews
              </Link>
              <Link to="/deals" className="text-wellness hover:text-wellness transition-all duration-200 font-medium text-base px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 whitespace-nowrap">
                Deals
              </Link>
            </div>

            {/* Apple-style Auth Actions with Proper Alignment */}
            <div className="flex items-center space-x-4">
              <div className="hidden lg:flex items-center space-x-4">
                {isAuthenticated ? (
                  <div className="flex items-center space-x-4">
                    <span className="text-wellness font-medium">
                      Welcome, {user?.email?.split('@')[0]}
                    </span>
                    <button
                      onClick={signOut}
                      className="text-wellness hover:text-wellness font-medium transition-colors px-3 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10"
                    >
                      Sign Out
                    </button>
                  </div>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        setAuthMode('signin');
                        setShowAuthModal(true);
                      }}
                      className="text-wellness hover:text-wellness font-medium transition-colors px-4 py-2 rounded-lg hover:bg-wellness hover:bg-opacity-10"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => {
                        setAuthMode('signup');
                        setShowAuthModal(true);
                      }}
                      className="bg-wellness text-white px-6 py-2 rounded-lg font-medium hover:bg-opacity-90 transition-all duration-200 shadow-sm"
                    >
                      Get Started
                    </button>
                  </>
                )}
              </div>

              {/* Mobile menu button with proper alignment */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden text-wellness hover:text-wellness p-2 rounded-lg hover:bg-wellness hover:bg-opacity-10 transition-all duration-200"
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
          </div>
        </div>

        {/* Mobile Menu - DIRECTORY ORDER: NRT FIRST, SMOKELESS SECOND */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-wellness">
            <div className="px-4 py-4 space-y-4">
              <Link to="/nrt-directory" className="block text-wellness hover:text-wellness font-medium transition-colors">NRT Directory</Link>
              <Link to="/smokeless-alternatives" className="block text-wellness hover:text-wellness font-medium transition-colors">Smokeless Alternatives</Link>
              <Link to="/stores" className="block text-wellness hover:text-wellness font-medium transition-colors">Store Locator</Link>
              <Link to="/vendors" className="block text-wellness hover:text-wellness font-medium transition-colors">Online Vendors</Link>
              <Link to="/price-compare" className="block text-wellness hover:text-wellness font-medium transition-colors">Price Compare</Link>
              <Link to="/reviews" className="block text-wellness hover:text-wellness font-medium transition-colors">Reviews</Link>
              <Link to="/deals" className="block text-wellness hover:text-wellness font-medium transition-colors">Deals</Link>
              <div className="pt-4 border-t border-wellness space-y-3">
                {isAuthenticated ? (
                  <>
                    <div className="text-wellness font-medium">
                      Welcome, {user?.email?.split('@')[0]}
                    </div>
                    <button
                      onClick={signOut}
                      className="block w-full text-left text-wellness font-medium"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => {
                        setAuthMode('signin');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left text-wellness font-medium"
                    >
                      Sign In
                    </button>
                    <button
                      onClick={() => {
                        setAuthMode('signup');
                        setShowAuthModal(true);
                        setIsMenuOpen(false);
                      }}
                      className="block w-full text-left bg-wellness text-white px-6 py-3 rounded-xl font-medium text-center"
                    >
                      Get Started
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
        </div>
      </nav>

      {/* Accessibility Skip Links */}
      <div className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50">
        <a href="#main-content" className="bg-wellness text-white px-6 py-3 rounded-xl">
          Skip to main content
        </a>
      </div>

      {/* Main Content */}
      <main id="main-content" className="min-h-screen bg-white">
        {/* Sophisticated Hero Section with Apple-style Layout */}
        <div className="relative max-w-7xl mx-auto px-8 pt-24 pb-32 overflow-hidden">
          {/* Clean Apple-style Background - Minimal Healthcare Aesthetic */}
          <div className="absolute inset-0 opacity-5 pointer-events-none">
            {/* Clean geometric accent - Apple minimal style */}
            <div className="absolute top-20 right-20 w-px h-32 bg-wellness"></div>
            <div className="absolute bottom-20 left-20 w-32 h-px bg-wellness"></div>
          </div>

          <div className="text-center relative space-y-10">
            {/* Enhanced NRT Platform Badge */}
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-muted via-muted/95 to-muted/90 text-wellness px-8 md:px-10 py-4 md:py-5 rounded-full text-sm font-semibold border border-border/40 backdrop-blur-xl shadow-xl shadow-wellness/10" role="banner" aria-label="NRT platform features">
              <Zap className="w-4 h-4 animate-pulse drop-shadow-sm" aria-hidden="true" />
              <span className="hidden sm:inline tracking-wide font-medium">World's Largest NRT Directory, Store Locator & Price Comparison</span>
              <span className="sm:hidden tracking-wide font-medium">NRT Directory & Store Locator</span>
              <Award className="w-4 h-4 drop-shadow-sm" aria-hidden="true" />
            </div>

            {/* Apple-style Hero Header with San Francisco Typography */}
            <header className="mb-16">
              <h1 className="text-6xl sm:text-7xl md:text-8xl font-black leading-[0.85] tracking-tight">
                <span className="block mb-4 text-wellness drop-shadow-sm font-bold">Craving Hit? </span>
                <span className="bg-gradient-to-r from-wellness via-wellness/95 to-wellness/80 bg-clip-text text-transparent font-black tracking-tighter drop-shadow-lg">
                  Find NRT Help Now
                </span>
              </h1>
            </header>

            {/* URGENT CRAVING ALERT - RED FOR URGENCY */}
            <div className="bg-alert-red/10 border border-alert-red/30 rounded-xl px-6 md:px-8 py-5 md:py-6 max-w-2xl mx-auto shadow-sm backdrop-blur-sm mb-8">
              <p className="text-alert-red font-bold text-center leading-relaxed text-base md:text-lg tracking-wide">
                Don't let cravings win. Find nicotine replacement therapy near you in seconds.
              </p>
            </div>

            {/* Apple-style Body Text with Proper Hierarchy */}
            <div className="text-xl md:text-2xl text-wellness mb-12 max-w-4xl mx-auto leading-relaxed space-y-6">
              <p className="font-medium leading-relaxed tracking-wide">
                Find nearest stores that sell NRT products when cravings hit.
                Compare prices across {loading ? (
                  <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-12 h-6 align-middle"></span>
                ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products from multiple vendors for the best deals.
                Browse reviews of every NRT product with detailed specifications.
              </p>
              <p className="font-bold text-wellness text-2xl md:text-3xl leading-tight tracking-tight">
                The internet's definitive NRT directory - like Vivino for wine, but for NRT.
              </p>
            </div>

            {/* Apple-style Hero Search Bar */}
            <div className="max-w-3xl mx-auto mb-16">
              <form onSubmit={handleSearch} className="relative" role="search">
                {/* Apple-style Search Icon */}
                <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <Search className="h-6 w-6 text-wellness" strokeWidth={2} />
                </div>
                {/* Apple-style Input Field */}
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  placeholder="Find nicotine gum, patches, lozenges near you..."
                  className="block w-full pl-16 pr-32 py-5 text-xl font-medium border-2 border-wellness rounded-xl bg-white focus:ring-4 focus:ring-wellness focus:border-wellness shadow-sm hover:shadow-md focus:shadow-lg transition-all duration-300 placeholder-wellness tracking-wide"
                  aria-label="Search for NRT products and stores"
                />
                {/* Apple-style Search Button */}
                <div className="absolute inset-y-0 right-0 pr-2 flex items-center">
                  <button
                    type="submit"
                    className="bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness focus:ring-4 focus:ring-wellness transition-all duration-300 font-semibold text-lg shadow-md hover:shadow-lg transform hover:scale-[1.02] tracking-wide"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>

            {/* Critical Call-to-Action Buttons - Apple Style */}
            <div className="max-w-4xl mx-auto mb-16">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                {/* Store Finder CTA - Urgent Craving Priority */}
                <Link
                  to="/stores"
                  className="group relative bg-gradient-to-br from-wellness via-wellness to-wellness/90 text-white px-8 md:px-10 py-6 md:py-8 rounded-2xl hover:from-wellness/95 hover:via-wellness hover:to-wellness focus:ring-4 focus:ring-wellness/30 transition-all duration-500 font-bold text-lg md:text-xl shadow-2xl shadow-wellness/25 hover:shadow-3xl hover:shadow-wellness/35 transform hover:scale-[1.03] hover:-translate-y-1 text-center flex flex-col items-center gap-4 overflow-hidden"
                  aria-label="Find stores near you that sell NRT products"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <Store className="w-9 h-9 group-hover:scale-110 transition-transform duration-300 drop-shadow-sm relative z-10" strokeWidth={2.5} />
                  <span className="relative z-10 tracking-wide">Find Store Near Me</span>
                  <span className="text-sm font-semibold opacity-90 relative z-10 tracking-wide">Urgent craving relief</span>
                </Link>

                {/* Price Compare CTA - Revenue Driver */}
                <Link
                  to="/compare"
                  className="group relative bg-gradient-to-br from-white via-white to-muted/30 border-2 border-wellness/20 text-wellness px-8 md:px-10 py-6 md:py-8 rounded-2xl hover:bg-gradient-to-br hover:from-wellness hover:via-wellness hover:to-wellness/90 hover:text-white hover:border-wellness focus:ring-4 focus:ring-wellness/30 transition-all duration-500 font-bold text-lg md:text-xl shadow-xl shadow-wellness/10 hover:shadow-2xl hover:shadow-wellness/25 transform hover:scale-[1.03] hover:-translate-y-1 text-center flex flex-col items-center gap-4 overflow-hidden backdrop-blur-sm"
                  aria-label="Compare prices across vendors for best NRT deals"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <DollarSign className="w-9 h-9 group-hover:scale-110 transition-transform duration-300 drop-shadow-sm relative z-10" strokeWidth={2.5} />
                  <span className="relative z-10 tracking-wide">Compare Prices Now</span>
                  <span className="text-sm font-semibold opacity-75 group-hover:opacity-90 relative z-10 tracking-wide">Save money on NRT</span>
                </Link>

                {/* NRT Directory CTA - Product Discovery */}
                <Link
                  to="/nrt-directory"
                  className="group relative bg-gradient-to-br from-white via-white to-muted/30 border-2 border-wellness/20 text-wellness px-8 md:px-10 py-6 md:py-8 rounded-2xl hover:bg-gradient-to-br hover:from-wellness hover:via-wellness hover:to-wellness/90 hover:text-white hover:border-wellness focus:ring-4 focus:ring-wellness/30 transition-all duration-500 font-bold text-lg md:text-xl shadow-xl shadow-wellness/10 hover:shadow-2xl hover:shadow-wellness/25 transform hover:scale-[1.03] hover:-translate-y-1 text-center flex flex-col items-center gap-4 overflow-hidden backdrop-blur-sm"
                  aria-label="Browse comprehensive NRT product directory"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <BookOpen className="w-9 h-9 group-hover:scale-110 transition-transform duration-300 drop-shadow-sm relative z-10" strokeWidth={2.5} />
                  <span className="relative z-10 tracking-wide">Browse NRT Directory</span>
                  <span className="text-sm font-semibold opacity-75 group-hover:opacity-90 relative z-10 tracking-wide">Explore all products</span>
                </Link>
              </div>
            </div>

            {/* Apple-style Filter Buttons */}
            <nav className="max-w-3xl mx-auto mb-12" aria-label="Product categories">
              <div className="flex flex-wrap justify-center gap-2 md:gap-3">
                <Link
                  to="/products"
                  className="bg-white backdrop-blur-sm text-wellness px-6 py-3 rounded-xl border border-wellness hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse all NRT products"
                >
                  All Products
                </Link>
                <Link
                  to="/products?category=gum"
                  className="bg-white backdrop-blur-sm text-wellness px-6 py-3 rounded-xl border border-wellness hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine gum products"
                >
                  Gum
                </Link>
                <Link
                  to="/products?category=patch"
                  className="bg-white backdrop-blur-sm text-wellness px-6 py-3 rounded-xl border border-wellness hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine patch products"
                >
                  Patch
                </Link>
                <Link
                  to="/products?category=lozenge"
                  className="bg-white backdrop-blur-sm text-wellness px-6 py-3 rounded-xl border border-wellness hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine lozenge products"
                >
                  Lozenge
                </Link>
                <Link
                  to="/products?category=spray"
                  className="bg-white backdrop-blur-sm text-wellness px-6 py-3 rounded-xl border border-wellness hover:border-wellness hover:bg-wellness hover:text-white transition-all duration-300 font-medium shadow-sm hover:shadow-lg focus:ring-2 focus:ring-wellness focus:outline-none text-sm md:text-base"
                  aria-label="Browse nicotine spray products"
                >
                  Spray
                </Link>
              </div>
            </nav>

            {/* Apple-style Priority Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 md:gap-6 justify-center items-center mb-16 md:mb-20" role="group" aria-label="Main action options">
              <Link
                to="/store-locator"
                className="group relative bg-wellness text-white px-8 md:px-12 py-4 md:py-5 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg overflow-hidden focus:ring-2 focus:ring-ring focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Find NRT stores near your location"
              >
                <div className="absolute inset-0 bg-wellness/90 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Search className="w-5 md:w-6 h-5 md:h-6 relative z-10 group-hover:rotate-6 transition-transform duration-200" strokeWidth={2} aria-hidden="true" />
                <span className="relative z-10">Find NRT Store Near Me</span>
                <div className="absolute inset-0 bg-white/10 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500 skew-x-12"></div>
              </Link>

              <Link
                to="/online-vendors"
                className="group bg-white text-wellness px-8 md:px-12 py-4 md:py-5 rounded-xl font-semibold transition-all duration-300 border-2 border-wellness hover:border-wellness shadow-md hover:shadow-lg hover:scale-[1.02] flex items-center gap-3 text-base md:text-lg focus:ring-4 focus:ring-wellness focus:outline-none w-full sm:w-auto justify-center sm:justify-start"
                aria-label="Compare prices from online NRT vendors"
              >
                <DollarSign className="w-5 md:w-6 h-5 md:h-6 group-hover:scale-105 transition-transform duration-200" strokeWidth={2.5} aria-hidden="true" />
                <span>Compare Online Vendors</span>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-wellness mb-16">
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-wellness">
                <Shield className="w-4 h-4 text-wellness" />
                <span>FDA-Approved Products</span>
              </div>
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-wellness">
                <Award className="w-4 h-4 text-wellness" />
                <span>Expert Reviewed</span>
              </div>
              <div className="flex items-center gap-2 bg-white backdrop-blur-sm px-4 py-2 rounded-full border border-wellness">
                <Users className="w-4 h-4 text-wellness" />
                <span>{loading ? (
                  <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-24 h-4"></span>
                ) : 'Community Reviewed'}</span>
              </div>
            </div>

            {/* Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-10 max-w-7xl mx-auto">
              <div className="group relative bg-gradient-to-br from-white via-white to-muted/20 backdrop-blur-2xl rounded-3xl p-10 shadow-2xl shadow-wellness/10 border border-wellness/10 text-center hover:shadow-3xl hover:shadow-wellness/20 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 via-transparent to-wellness/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-wellness via-wellness to-wellness/90 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                    <Search className="w-10 h-10 text-white drop-shadow-sm" strokeWidth={2} />
                  </div>
                  <h3 className="text-2xl font-black text-wellness mb-4 tracking-tight">NRT Store Locator</h3>
                  <p className="text-wellness/80 leading-relaxed text-lg font-medium">Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.</p>
                </div>
              </div>

              <div className="group relative bg-gradient-to-br from-white via-white to-muted/20 backdrop-blur-2xl rounded-3xl p-10 shadow-2xl shadow-wellness/10 border border-wellness/10 text-center hover:shadow-3xl hover:shadow-wellness/20 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 via-transparent to-wellness/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-wellness via-wellness to-wellness/90 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                    <DollarSign className="w-10 h-10 text-white drop-shadow-sm" strokeWidth={2} />
                  </div>
                  <h3 className="text-2xl font-black text-wellness mb-4 tracking-tight">Online Vendor Price Comparison</h3>
                  <p className="text-wellness/80 leading-relaxed text-lg font-medium">Compare prices across {loading ? (
                    <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-12 h-5 align-middle"></span>
                  ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products from multiple vendors. Check delivery options & discounts.</p>
                </div>
              </div>

              <div className="group relative bg-gradient-to-br from-white via-white to-muted/20 backdrop-blur-2xl rounded-3xl p-10 shadow-2xl shadow-wellness/10 border border-wellness/10 text-center hover:shadow-3xl hover:shadow-wellness/20 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-2 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 via-transparent to-wellness/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="w-20 h-20 bg-gradient-to-br from-wellness via-wellness to-wellness/90 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                    <Star className="w-10 h-10 text-white drop-shadow-sm" strokeWidth={2} />
                  </div>
                  <h3 className="text-2xl font-black text-wellness mb-4 tracking-tight">Product Reviews & Ratings</h3>
                  <p className="text-wellness/80 leading-relaxed text-lg font-medium">Read authentic reviews from real users. Compare effectiveness, taste, and value across {loading ? (
                    <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-12 h-5 align-middle"></span>
                  ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Apple-style Secondary Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-black text-foreground mb-8 leading-tight tracking-tight">
              <span className="bg-gradient-to-r from-foreground via-foreground/95 to-foreground/90 bg-clip-text text-transparent">The Vivino for </span>
              <span className="bg-gradient-to-r from-wellness via-wellness/95 to-wellness/80 bg-clip-text text-transparent">NRT Products</span>
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-medium tracking-wide">
              Discover, rate, and find the best NRT deals with our comprehensive directory and price comparison platform
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="grid md:grid-cols-3 gap-10 max-w-5xl">
              <div className="text-center group">
                <div className="bg-gradient-to-br from-wellness via-wellness to-wellness/90 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                  <Search className="w-10 h-10 text-white drop-shadow-sm" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3 flex items-center justify-center gap-3 tracking-tight">
                  <Store className="w-6 h-6 text-wellness" />
                  NRT Store Locator
                </h3>
                <p className="text-muted-foreground text-lg leading-relaxed font-medium">
                  Find nearest pharmacies, supermarkets & stores that sell NRT when cravings hit. Real-time inventory & pricing.
                </p>
              </div>
              <div className="text-center group">
                <div className="bg-gradient-to-br from-wellness via-wellness to-wellness/90 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                  <DollarSign className="w-10 h-10 text-white drop-shadow-sm" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3 flex items-center justify-center gap-3 tracking-tight">
                  <DollarSign className="w-6 h-6 text-wellness" />
                  Online Vendor Price Comparison
                </h3>
                <p className="text-muted-foreground text-lg leading-relaxed font-medium">
                  Compare prices across {loading ? (
                    <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-12 h-4 align-middle"></span>
                  ) : products.length > 0 ? `${products.length}+` : 'available'} NRT products from multiple vendors. Check delivery options & discounts.
                </p>
              </div>
              <div className="text-center group">
                <div className="bg-gradient-to-br from-wellness via-wellness to-wellness/90 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-wellness/25 group-hover:scale-110 transition-transform duration-300">
                  <Star className="w-10 h-10 text-white drop-shadow-sm" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-3 flex items-center justify-center gap-3 tracking-tight">
                  <BookOpen className="w-6 h-6 text-wellness" />
                  Complete NRT Directory
                </h3>
                <p className="text-muted-foreground text-lg leading-relaxed font-medium">
                  Professional reviews of every NRT product. Detailed specs: nicotine strength, flavor, effectiveness.
                </p>
              </div>
            </div>

            <div className="relative bg-gradient-to-br from-muted via-muted/95 to-muted/90 rounded-3xl p-16 text-center overflow-hidden shadow-2xl shadow-wellness/10 border border-wellness/10">
              <div className="absolute inset-0 bg-gradient-to-br from-wellness/5 via-transparent to-wellness/10 opacity-50"></div>
              <div className="relative z-10">
                <div className="w-24 h-24 bg-gradient-to-br from-white via-white to-muted/30 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-xl shadow-wellness/20">
                  <Target className="w-12 h-12 text-wellness drop-shadow-sm" />
                </div>
                <h3 className="text-4xl font-black text-foreground mb-6 tracking-tight">Start Your Journey</h3>
                <p className="text-muted-foreground mb-10 text-xl leading-relaxed font-medium max-w-2xl mx-auto">
                  {loading ? (
                    <span className="inline-flex items-center gap-2">
                      Loading community data
                      <span className="inline-block bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse rounded w-16 h-5"></span>
                    </span>
                  ) : testimonials.length > 0 ? `Join ${testimonials.length} users who have found their perfect NRT solution` :
                   'Join our growing community of users finding their perfect NRT solution'}
                </p>
                <Link
                  to="/products"
                  className="group relative inline-flex items-center gap-4 bg-gradient-to-r from-wellness via-wellness to-wellness/90 text-white px-12 py-6 rounded-2xl hover:from-wellness/95 hover:via-wellness hover:to-wellness focus:ring-4 focus:ring-wellness/30 shadow-2xl shadow-wellness/25 hover:shadow-3xl hover:shadow-wellness/35 font-bold text-xl transition-all duration-500 transform hover:scale-[1.05] hover:-translate-y-1 tracking-wide overflow-hidden"
                  onClick={() => {
                    // Track CTA click for analytics
                    if (typeof (window as any).gtag !== 'undefined') {
                      (window as any).gtag('event', 'cta_click', {
                        event_category: 'engagement',
                        event_label: 'homepage_get_started'
                      });
                    }
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="relative z-10">Get Started</span>
                  <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300 relative z-10 drop-shadow-sm" strokeWidth={2.5} />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products Section - Real NRT Data */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-black text-foreground mb-8 leading-tight tracking-tight">
              <span className="bg-gradient-to-r from-foreground via-foreground/95 to-foreground/90 bg-clip-text text-transparent">Featured </span>
              <span className="bg-gradient-to-r from-wellness via-wellness/95 to-wellness/80 bg-clip-text text-transparent">NRT Products</span>
            </h2>
            <p className="text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed font-medium tracking-wide">
              Discover top-rated nicotine replacement therapy products from trusted brands
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {[...Array(8)].map((_, i) => (
                <ProductCardSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-16 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl border border-red-200">
              <div className="w-16 h-16 bg-alert-red rounded-full flex items-center justify-center mx-auto mb-6">
                <X className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-alert-red mb-3">Unable to Load Products</h3>
              <p className="text-red-700 mb-6 max-w-md mx-auto">We're experiencing technical difficulties. Please try refreshing the page or check back later.</p>
              <button
                onClick={() => window.location.reload()}
                className="bg-alert-red text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium"
              >
                Refresh Page
              </button>
            </div>
          ) : products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {products.slice(0, 8).map((product) => (
                <Link
                  key={product.id}
                  to={`/product/${product.id}`}
                  className="bg-white rounded-xl shadow-sm hover:shadow-xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 overflow-hidden group border border-border hover:border-wellness"
                  aria-label={`View details for ${product.name} by ${product.brand}`}
                >
                  <div className="aspect-square bg-muted flex items-center justify-center p-6">
                    {product.image_url ? (
                      <img
                        src={product.image_url}
                        alt={`${product.name} by ${product.brand} - NRT product image`}
                        className="w-full h-full object-cover rounded-xl"
                        loading="lazy"
                        decoding="async"
                      />
                    ) : (
                      <Package className="w-16 h-16 text-muted-foreground" />
                    )}
                  </div>
                  <div className="p-6">
                    <h3 className="font-semibold text-foreground mb-2 group-hover:text-wellness transition-colors">
                      {product.name}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-3">{product.brand}</p>
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-rating-gold fill-current" />
                        <span className="text-sm font-medium">
                          {product.user_rating_avg ? product.user_rating_avg.toFixed(1) : 'N/A'}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        ({product.user_rating_count || 0} reviews)
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2 mb-4">{product.description}</p>

                    {/* Quick View Button */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <button
                        className="bg-white text-foreground px-6 py-3 rounded-xl font-medium hover:bg-muted transition-colors"
                        onClick={(e) => {
                          e.preventDefault();
                          // Quick view modal functionality
                          console.log('Quick view:', product.name);
                        }}
                      >
                        Quick View
                      </button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="bg-red-50 border border-alert-red rounded-xl p-6 max-w-md mx-auto">
                <div className="flex items-center gap-2 text-alert-red mb-2">
                  <Package className="w-5 h-5" />
                  <span className="font-medium">Failed to load products</span>
                </div>
                <p className="text-alert-red text-sm">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 bg-alert-red text-white px-6 py-3 rounded-xl hover:bg-alert-red/90 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-20 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
              <div className="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-8">
                <Package className="w-10 h-10 text-gray-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">No Products Available</h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                We're currently updating our product database. Please check back soon for the latest NRT products and reviews.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-wellness text-white px-6 py-3 rounded-lg hover:bg-wellness/90 transition-colors font-medium"
                >
                  Refresh Page
                </button>
                <Link
                  to="/contact"
                  className="bg-white text-wellness border border-wellness px-6 py-3 rounded-lg hover:bg-wellness/10 transition-colors font-medium"
                >
                  Contact Support
                </Link>
              </div>
            </div>
          )}

          <div className="text-center mt-12">
            <Link
              to="/products"
              className="inline-flex items-center gap-2 bg-wellness text-white px-8 py-4 rounded-xl hover:bg-wellness font-semibold transition-all duration-200"
            >
              View All Products
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter Signup Section */}
      <section className="bg-wellness py-16">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Stay Updated on NRT Deals</h2>
          <p className="text-white/80 mb-8 text-lg">Get notified about price drops, new products, and exclusive discounts.</p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" onSubmit={handleNewsletterSignup}>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              required
              className="flex-1 px-4 py-3 rounded-xl border-2 border-white/20 bg-white/95 backdrop-blur-sm focus:ring-4 focus:ring-white/30 focus:border-white focus:outline-none font-medium text-foreground placeholder-muted-foreground shadow-md hover:shadow-lg transition-all duration-300"
              aria-label="Email address for newsletter"
            />
            <button
              type="submit"
              disabled={newsletterStatus === 'loading'}
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-md hover:shadow-lg hover:scale-[1.02] tracking-wide ${
                newsletterStatus === 'loading'
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-white text-wellness hover:bg-muted'
              }`}
            >
              {newsletterStatus === 'loading' ? 'Subscribing...' : 'Subscribe'}
            </button>
          </form>

          {/* Newsletter feedback messages */}
          {newsletterMessage && (
            <div className={`mt-4 p-4 rounded-xl max-w-md mx-auto text-center ${
              newsletterStatus === 'success'
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-red-100 text-red-800 border border-red-200'
            }`}>
              <p className="font-medium">{newsletterMessage}</p>
            </div>
          )}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-foreground mb-12">What Our Users Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.length > 0 ? (
              testimonials.map((testimonial) => (
                <div key={testimonial.id} className="bg-muted rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${i < testimonial.rating ? 'text-rating-gold fill-current' : 'text-muted-foreground'}`}
                      />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4">"{testimonial.testimonial_text}"</p>
                  <div className="flex items-center justify-between">
                    <p className="font-semibold text-foreground">{testimonial.user_name}</p>
                    {testimonial.verified_purchase && (
                      <CheckCircle className="w-4 h-4 text-wellness" />
                    )}
                  </div>
                  {testimonial.days_smoke_free && (
                    <p className="text-xs text-wellness mt-2">
                      {testimonial.days_smoke_free} days smoke-free
                    </p>
                  )}
                </div>
              ))
            ) : loading ? (
              // Loading state - no hardcoded data
              [...Array(3)].map((_, i) => (
                <div key={i} className="bg-muted rounded-xl p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(5)].map((_, j) => (
                      <div key={j} className="w-4 h-4 bg-muted rounded"></div>
                    ))}
                  </div>
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded mb-4 w-3/4"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                </div>
              ))
            ) : (
              // No testimonials available - no hardcoded fallback
              <div className="col-span-full text-center py-12">
                {loading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <TestimonialSkeleton />
                    <TestimonialSkeleton />
                    <TestimonialSkeleton />
                  </div>
                ) : (
                  <p className="text-muted-foreground font-medium">No testimonials available at the moment.</p>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Apple-style Footer */}
      <footer className="bg-white border-t border-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-wellness rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-sm">N</span>
                </div>
                <span className="text-lg font-bold text-wellness tracking-tight">NRTList</span>
              </div>
              <p className="text-wellness font-medium leading-relaxed">
                The premier platform for nicotine replacement therapy discovery and comparison.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-wellness tracking-tight">Products</h4>
              <ul className="space-y-3 text-wellness">
                <li><Link to="/products" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Browse All</Link></li>
                <li><Link to="/products?category=pouches" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Pouches</Link></li>
                <li><Link to="/products?category=gum" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Gum</Link></li>
                <li><Link to="/products?category=lozenges" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Lozenges</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-wellness tracking-tight">Resources</h4>
              <ul className="space-y-3 text-wellness">
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Help Center</Link></li>
                <li><Link to="/community" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Community</Link></li>
                <li><Link to="/progress" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Progress Tracking</Link></li>
                <li><Link to="/reviews" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Reviews</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-6 text-wellness tracking-tight">Company</h4>
              <ul className="space-y-3 text-wellness">
                <li><Link to="/about" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">About</Link></li>
                <li><Link to="/privacy" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Privacy</Link></li>
                <li><Link to="/terms" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Terms</Link></li>
                <li><Link to="/contact" className="hover:text-wellness transition-colors duration-200 font-medium hover:translate-x-1 inline-block">Contact</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-wellness mt-16 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-wellness font-medium tracking-tight">&copy; 2024 NRTList. All rights reserved.</p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <a
                  href="https://twitter.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-wellness hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </a>
                <a
                  href="https://facebook.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-wellness hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M20 10C20 4.477 15.523 0 10 0S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z" clipRule="evenodd" />
                  </svg>
                </a>
                <a
                  href="https://instagram.com/nrtlist"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-wellness hover:text-wellness transition-all duration-300 hover:scale-110"
                  aria-label="Follow us on Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm4.5 6.5h-9v7h9v-7z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>

      {/* Auth Modal */}
      {showAuthModal && (
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
        />
      )}
    </div>
  );
};

export default LandingPage;
